# Multi-Purpose HTML5 Template

A comprehensive, modern HTML5 template with both frontend pages and admin dashboard, built with Tailwind CSS and Vue.js. Perfect for businesses, agencies, and projects that need a professional web presence with content management capabilities.

## 🚀 Features

### Frontend Pages
- **Homepage** - Modern landing page with hero section, features, and call-to-action
- **About Us** - Company story, values, and team showcase
- **Services** - Detailed service offerings with process overview
- **Portfolio** - Project showcase with filtering and modal views
- **Blog** - Article listing with sidebar and search functionality
- **Pricing** - Flexible pricing plans with annual/monthly toggle
- **Contact** - Contact form with validation and FAQ section

### Admin Dashboard
- **Dashboard Overview** - Analytics, charts, and quick actions
- **User Management** - Complete CRUD operations for users
- **Content Management** - Blog posts and page content editing
- **Analytics** - Traffic and performance metrics
- **Settings** - System configuration and preferences
- **Authentication** - Login/register with demo credentials

### Technical Features
- **Responsive Design** - Mobile-first approach, works on all devices
- **Modern CSS** - Tailwind CSS with custom components
- **Interactive UI** - Vue.js for dynamic functionality
- **Clean Code** - Well-structured, commented, and maintainable
- **Font Integration** - Google Open Sans with Roboto fallback
- **Icon Library** - Font Awesome icons throughout
- **Form Validation** - Client-side validation with user feedback
- **Notification System** - Toast notifications for user actions
- **Modal Windows** - Reusable modal components
- **Data Tables** - Sortable, filterable admin tables
- **Charts & Analytics** - Chart.js integration for data visualization

## 📁 Project Structure

```
template-name/
│
├── assets/                 # Static assets
│   ├── css/               # Compiled CSS and source files
│   │   ├── style.css      # Compiled Tailwind CSS
│   │   └── tailwind.css   # Source Tailwind file
│   ├── js/                # JavaScript files
│   │   └── main.js        # Vue.js application
│   ├── fonts/             # Web fonts (if needed)
│   └── images/            # Images and icons
│
├── html/                  # Frontend pages
│   ├── index.html         # Homepage
│   ├── about.html         # About page
│   ├── services.html      # Services page
│   ├── portfolio.html     # Portfolio page
│   ├── blog.html          # Blog page
│   ├── pricing.html       # Pricing page
│   └── contact.html       # Contact page
│
├── admin/                 # Admin dashboard
│   ├── login.html         # Admin login
│   ├── dashboard.html     # Main dashboard
│   ├── users.html         # User management
│   ├── content.html       # Content management
│   ├── analytics.html     # Analytics page
│   └── settings.html      # Settings page
│
├── node_modules/          # Dependencies (after npm install)
├── package.json           # Project dependencies
├── tailwind.config.js     # Tailwind configuration
└── README.md             # This file
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Quick Start

1. **Clone or download** the template files
2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Build CSS** (for development with watch mode):
   ```bash
   npm run dev
   ```

4. **Build for production**:
   ```bash
   npm run build
   ```

5. **Serve locally** (optional):
   ```bash
   npm run serve
   ```

6. **Open in browser**:
   - Frontend: Open `html/index.html`
   - Admin: Open `admin/login.html`

### Admin Demo Credentials
- **Email**: <EMAIL>
- **Password**: admin123

## 🎨 Customization

### Colors & Branding
1. **Update brand colors** in `tailwind.config.js`:
   ```javascript
   colors: {
     primary: {
       // Your brand colors
     }
   }
   ```

2. **Change logo/brand name**:
   - Replace "YourBrand" throughout the HTML files
   - Update logo images in the assets folder

3. **Fonts**:
   - Default: Google Open Sans with Roboto fallback
   - Modify in `assets/css/tailwind.css` if needed

### Content Customization
1. **Replace placeholder content** in HTML files
2. **Update images** in the `assets/images/` folder
3. **Modify contact information** in footer and contact page
4. **Customize service offerings** in services page
5. **Add your portfolio projects** in portfolio page

### Styling
1. **Custom components** are defined in `assets/css/tailwind.css`
2. **Modify existing styles** or add new ones
3. **Rebuild CSS** after changes: `npm run build`

## 🔧 Vue.js Functionality

### Interactive Features
- **Mobile menu toggle**
- **Form validation and submission**
- **Modal windows**
- **Notification system**
- **Admin sidebar toggle**
- **Data table interactions**
- **Search and filtering**
- **Smooth scrolling navigation**

### Extending Functionality
The Vue.js app is initialized in `assets/js/main.js`. You can:
- Add new reactive data properties
- Create custom methods
- Implement API integrations
- Add more interactive components

## 📱 Responsive Design

The template is fully responsive and tested on:
- **Desktop** (1200px+)
- **Tablet** (768px - 1199px)
- **Mobile** (320px - 767px)

Key responsive features:
- Mobile-first CSS approach
- Collapsible navigation menu
- Responsive admin sidebar
- Flexible grid layouts
- Touch-friendly interactions

## 🚀 Deployment

### Static Hosting
1. Build the CSS: `npm run build`
2. Upload all files to your web server
3. Ensure proper file permissions
4. Configure server for SPA routing (if needed)

### Popular Hosting Options
- **Netlify** - Drag and drop deployment
- **Vercel** - Git-based deployment
- **GitHub Pages** - Free hosting for public repos
- **Traditional hosting** - Upload via FTP/SFTP

## 🔒 Security Notes

### For Production Use
1. **Remove demo credentials** from login page
2. **Implement proper authentication** backend
3. **Validate all forms** server-side
4. **Sanitize user inputs**
5. **Use HTTPS** for all pages
6. **Implement CSRF protection**

## 🎯 Browser Support

- **Chrome** (latest)
- **Firefox** (latest)
- **Safari** (latest)
- **Edge** (latest)
- **Mobile browsers** (iOS Safari, Chrome Mobile)

## 📄 License

This template is provided as-is for personal and commercial use. Feel free to modify and distribute according to your needs.

## 🤝 Support

For questions or customization help:
- Check the code comments for guidance
- Refer to Tailwind CSS documentation
- Consult Vue.js documentation for interactive features

## 🔄 Updates

To update dependencies:
```bash
npm update
npm run build
```

## 📋 Checklist for Going Live

- [ ] Replace all placeholder content
- [ ] Update contact information
- [ ] Add real images and optimize them
- [ ] Test all forms and interactions
- [ ] Implement proper backend authentication
- [ ] Set up analytics tracking
- [ ] Configure SEO meta tags
- [ ] Test on multiple devices and browsers
- [ ] Set up SSL certificate
- [ ] Configure domain and hosting

---

**Built with ❤️ using Tailwind CSS, Vue.js, and modern web standards.**
