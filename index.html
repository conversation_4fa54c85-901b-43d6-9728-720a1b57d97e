<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Purpose Template - Demo</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Open Sans', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            max-width: 800px;
            padding: 2rem;
        }
        
        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .link-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .link-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
        }
        
        .link-card h3 {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
        }
        
        .link-card p {
            font-size: 0.9rem;
            margin: 0;
            opacity: 0.8;
        }
        
        .admin-note {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 2rem;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }
            
            .links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Multi-Purpose Template</h1>
        <p>A comprehensive HTML5 template with frontend pages and admin dashboard</p>
        
        <div class="links">
            <a href="html/index.html" class="link-card">
                <h3>🏠 Frontend Website</h3>
                <p>Explore the complete frontend with all pages including home, about, services, portfolio, blog, pricing, and contact.</p>
            </a>
            
            <a href="admin/login.html" class="link-card">
                <h3>⚙️ Admin Dashboard</h3>
                <p>Access the admin panel with user management, analytics, content management, and settings.</p>
            </a>
        </div>
        
        <div class="admin-note">
            <strong>Admin Demo Credentials:</strong><br>
            Email: <EMAIL><br>
            Password: admin123
        </div>
        
        <div style="margin-top: 2rem; font-size: 0.9rem; opacity: 0.8;">
            <p>Built with Tailwind CSS, Vue.js, and modern web standards</p>
        </div>
    </div>
</body>
</html>
