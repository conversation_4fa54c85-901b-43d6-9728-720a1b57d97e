<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog - Multi-Purpose Template</title>
    <meta name="description" content="Read our latest articles, insights, and updates about web development and digital marketing">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans">
    <div id="app">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
            <div class="container-custom">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <a href="index.html" class="text-2xl font-bold text-gradient">YourBrand</a>
                    </div>
                    
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors">Home</a>
                        <a href="about.html" class="text-gray-700 hover:text-blue-600 transition-colors">About</a>
                        <a href="services.html" class="text-gray-700 hover:text-blue-600 transition-colors">Services</a>
                        <a href="portfolio.html" class="text-gray-700 hover:text-blue-600 transition-colors">Portfolio</a>
                        <a href="blog.html" class="text-blue-600 font-medium">Blog</a>
                        <a href="pricing.html" class="text-gray-700 hover:text-blue-600 transition-colors">Pricing</a>
                        <a href="contact.html" class="text-gray-700 hover:text-blue-600 transition-colors">Contact</a>
                        <a href="../admin/login.html" class="btn-primary">Admin</a>
                    </div>
                    
                    <button @click="toggleMobileMenu" class="md:hidden mobile-menu-button">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
                
                <div v-show="mobileMenuOpen" class="md:hidden mobile-menu bg-white border-t border-gray-200 py-4">
                    <div class="flex flex-col space-y-4">
                        <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Home</a>
                        <a href="about.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">About</a>
                        <a href="services.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Services</a>
                        <a href="portfolio.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Portfolio</a>
                        <a href="blog.html" class="text-blue-600 font-medium px-4">Blog</a>
                        <a href="pricing.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Pricing</a>
                        <a href="contact.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Contact</a>
                        <a href="../admin/login.html" class="btn-primary mx-4 text-center">Admin</a>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Page Header -->
        <section class="bg-gradient-to-br from-blue-50 to-purple-50 pt-20">
            <div class="container-custom section-padding">
                <div class="text-center">
                    <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">Our Blog</h1>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                        Stay updated with the latest trends, tips, and insights in web development and digital marketing
                    </p>
                    
                    <!-- Search Bar -->
                    <div class="max-w-md mx-auto">
                        <div class="relative">
                            <input 
                                type="text" 
                                v-model="searchQuery"
                                placeholder="Search articles..." 
                                class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Blog Content -->
        <section class="section-padding bg-white">
            <div class="container-custom">
                <div class="grid lg:grid-cols-4 gap-8">
                    <!-- Main Content -->
                    <div class="lg:col-span-3">
                        <!-- Featured Post -->
                        <div class="card mb-8 overflow-hidden">
                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <img src="../assets/images/blog-featured.jpg" alt="Featured Post" 
                                         class="w-full h-64 object-cover rounded-lg"
                                         onerror="this.src='https://via.placeholder.com/600x400/3B82F6/FFFFFF?text=Featured+Post'">
                                </div>
                                <div class="flex flex-col justify-center">
                                    <div class="flex items-center space-x-4 mb-3">
                                        <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">Featured</span>
                                        <span class="text-sm text-gray-500">January 15, 2024</span>
                                    </div>
                                    <h2 class="text-2xl font-bold text-gray-900 mb-3">
                                        The Future of Web Development: Trends to Watch in 2024
                                    </h2>
                                    <p class="text-gray-600 mb-4">
                                        Discover the latest trends and technologies that are shaping the future of web development, 
                                        from AI integration to progressive web apps.
                                    </p>
                                    <a href="#" class="text-blue-600 hover:text-blue-700 font-medium">
                                        Read More →
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Blog Posts Grid -->
                        <div class="grid md:grid-cols-2 gap-8">
                            <!-- Blog Post 1 -->
                            <article class="card hover:shadow-custom-lg transition-shadow">
                                <img src="../assets/images/blog-1.jpg" alt="Blog Post" 
                                     class="w-full h-48 object-cover rounded-lg mb-4"
                                     onerror="this.src='https://via.placeholder.com/400x300/10B981/FFFFFF?text=Blog+Post'">
                                <div class="flex items-center space-x-4 mb-3">
                                    <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">Design</span>
                                    <span class="text-sm text-gray-500">January 12, 2024</span>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                    10 Essential UI/UX Design Principles for Better User Experience
                                </h3>
                                <p class="text-gray-600 mb-4">
                                    Learn the fundamental principles that every designer should know to create 
                                    intuitive and engaging user interfaces.
                                </p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <img src="../assets/images/author-1.jpg" alt="Author" 
                                             class="w-6 h-6 rounded-full object-cover"
                                             onerror="this.src='https://via.placeholder.com/24x24/3B82F6/FFFFFF?text=A'">
                                        <span class="text-sm text-gray-600">Sarah Johnson</span>
                                    </div>
                                    <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                                        Read More
                                    </a>
                                </div>
                            </article>
                            
                            <!-- Blog Post 2 -->
                            <article class="card hover:shadow-custom-lg transition-shadow">
                                <img src="../assets/images/blog-2.jpg" alt="Blog Post" 
                                     class="w-full h-48 object-cover rounded-lg mb-4"
                                     onerror="this.src='https://via.placeholder.com/400x300/8B5CF6/FFFFFF?text=Blog+Post'">
                                <div class="flex items-center space-x-4 mb-3">
                                    <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full">Development</span>
                                    <span class="text-sm text-gray-500">January 10, 2024</span>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                    Getting Started with Vue.js 3: A Comprehensive Guide
                                </h3>
                                <p class="text-gray-600 mb-4">
                                    A complete beginner's guide to Vue.js 3, covering everything from basic concepts 
                                    to advanced features and best practices.
                                </p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <img src="../assets/images/author-2.jpg" alt="Author" 
                                             class="w-6 h-6 rounded-full object-cover"
                                             onerror="this.src='https://via.placeholder.com/24x24/10B981/FFFFFF?text=M'">
                                        <span class="text-sm text-gray-600">Mike Chen</span>
                                    </div>
                                    <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                                        Read More
                                    </a>
                                </div>
                            </article>
                            
                            <!-- Blog Post 3 -->
                            <article class="card hover:shadow-custom-lg transition-shadow">
                                <img src="../assets/images/blog-3.jpg" alt="Blog Post" 
                                     class="w-full h-48 object-cover rounded-lg mb-4"
                                     onerror="this.src='https://via.placeholder.com/400x300/F59E0B/FFFFFF?text=Blog+Post'">
                                <div class="flex items-center space-x-4 mb-3">
                                    <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">SEO</span>
                                    <span class="text-sm text-gray-500">January 8, 2024</span>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                    SEO Best Practices for Modern Websites in 2024
                                </h3>
                                <p class="text-gray-600 mb-4">
                                    Stay ahead of the competition with these proven SEO strategies and techniques 
                                    that actually work in today's search landscape.
                                </p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <img src="../assets/images/author-3.jpg" alt="Author" 
                                             class="w-6 h-6 rounded-full object-cover"
                                             onerror="this.src='https://via.placeholder.com/24x24/F59E0B/FFFFFF?text=E'">
                                        <span class="text-sm text-gray-600">Emily Davis</span>
                                    </div>
                                    <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                                        Read More
                                    </a>
                                </div>
                            </article>
                            
                            <!-- Blog Post 4 -->
                            <article class="card hover:shadow-custom-lg transition-shadow">
                                <img src="../assets/images/blog-4.jpg" alt="Blog Post" 
                                     class="w-full h-48 object-cover rounded-lg mb-4"
                                     onerror="this.src='https://via.placeholder.com/400x300/EF4444/FFFFFF?text=Blog+Post'">
                                <div class="flex items-center space-x-4 mb-3">
                                    <span class="px-3 py-1 bg-red-100 text-red-800 text-sm rounded-full">Marketing</span>
                                    <span class="text-sm text-gray-500">January 5, 2024</span>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                    Digital Marketing Strategies That Actually Convert
                                </h3>
                                <p class="text-gray-600 mb-4">
                                    Discover proven digital marketing strategies that drive real results and 
                                    help businesses grow their online presence effectively.
                                </p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <img src="../assets/images/author-4.jpg" alt="Author" 
                                             class="w-6 h-6 rounded-full object-cover"
                                             onerror="this.src='https://via.placeholder.com/24x24/EF4444/FFFFFF?text=D'">
                                        <span class="text-sm text-gray-600">David Wilson</span>
                                    </div>
                                    <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                                        Read More
                                    </a>
                                </div>
                            </article>
                        </div>
                        
                        <!-- Pagination -->
                        <div class="flex justify-center mt-12">
                            <div class="flex space-x-2">
                                <button class="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50">
                                    Previous
                                </button>
                                <button class="px-4 py-2 bg-blue-600 text-white rounded-lg">1</button>
                                <button class="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50">2</button>
                                <button class="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50">3</button>
                                <button class="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50">
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sidebar -->
                    <div class="lg:col-span-1">
                        <!-- Categories -->
                        <div class="card mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
                            <ul class="space-y-2">
                                <li><a href="#" class="flex justify-between text-gray-600 hover:text-blue-600 transition-colors">
                                    <span>Web Development</span>
                                    <span class="text-sm text-gray-400">12</span>
                                </a></li>
                                <li><a href="#" class="flex justify-between text-gray-600 hover:text-blue-600 transition-colors">
                                    <span>Design</span>
                                    <span class="text-sm text-gray-400">8</span>
                                </a></li>
                                <li><a href="#" class="flex justify-between text-gray-600 hover:text-blue-600 transition-colors">
                                    <span>SEO</span>
                                    <span class="text-sm text-gray-400">6</span>
                                </a></li>
                                <li><a href="#" class="flex justify-between text-gray-600 hover:text-blue-600 transition-colors">
                                    <span>Marketing</span>
                                    <span class="text-sm text-gray-400">5</span>
                                </a></li>
                                <li><a href="#" class="flex justify-between text-gray-600 hover:text-blue-600 transition-colors">
                                    <span>Business</span>
                                    <span class="text-sm text-gray-400">4</span>
                                </a></li>
                            </ul>
                        </div>
                        
                        <!-- Recent Posts -->
                        <div class="card mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Posts</h3>
                            <div class="space-y-4">
                                <div class="flex space-x-3">
                                    <img src="../assets/images/blog-thumb-1.jpg" alt="Recent Post" 
                                         class="w-16 h-16 object-cover rounded-lg flex-shrink-0"
                                         onerror="this.src='https://via.placeholder.com/64x64/3B82F6/FFFFFF?text=1'">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 mb-1">
                                            <a href="#" class="hover:text-blue-600 transition-colors">
                                                Building Responsive Layouts with CSS Grid
                                            </a>
                                        </h4>
                                        <p class="text-xs text-gray-500">January 3, 2024</p>
                                    </div>
                                </div>
                                
                                <div class="flex space-x-3">
                                    <img src="../assets/images/blog-thumb-2.jpg" alt="Recent Post" 
                                         class="w-16 h-16 object-cover rounded-lg flex-shrink-0"
                                         onerror="this.src='https://via.placeholder.com/64x64/10B981/FFFFFF?text=2'">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 mb-1">
                                            <a href="#" class="hover:text-blue-600 transition-colors">
                                                JavaScript ES2024: New Features Overview
                                            </a>
                                        </h4>
                                        <p class="text-xs text-gray-500">January 1, 2024</p>
                                    </div>
                                </div>
                                
                                <div class="flex space-x-3">
                                    <img src="../assets/images/blog-thumb-3.jpg" alt="Recent Post" 
                                         class="w-16 h-16 object-cover rounded-lg flex-shrink-0"
                                         onerror="this.src='https://via.placeholder.com/64x64/8B5CF6/FFFFFF?text=3'">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 mb-1">
                                            <a href="#" class="hover:text-blue-600 transition-colors">
                                                Optimizing Website Performance in 2024
                                            </a>
                                        </h4>
                                        <p class="text-xs text-gray-500">December 28, 2023</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Newsletter -->
                        <div class="card bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Subscribe to Our Newsletter</h3>
                            <p class="text-gray-600 mb-4">Get the latest articles and insights delivered to your inbox.</p>
                            <form class="space-y-3">
                                <input type="email" placeholder="Your email address" class="input-field">
                                <button type="submit" class="btn-primary w-full">Subscribe</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Footer -->
        <footer class="bg-gray-900 text-white section-padding">
            <div class="container-custom">
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
                    <div>
                        <h3 class="text-xl font-bold mb-4 text-gradient">YourBrand</h3>
                        <p class="text-gray-400 mb-4">
                            Building amazing digital experiences with modern technology and creative design.
                        </p>
                        <div class="flex space-x-4">
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                        <ul class="space-y-2">
                            <li><a href="about.html" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                            <li><a href="services.html" class="text-gray-400 hover:text-white transition-colors">Services</a></li>
                            <li><a href="portfolio.html" class="text-gray-400 hover:text-white transition-colors">Portfolio</a></li>
                            <li><a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Services</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Web Design</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Development</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">SEO</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Consulting</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Contact Info</h4>
                        <div class="space-y-2 text-gray-400">
                            <p><i class="fas fa-envelope mr-2"></i> <EMAIL></p>
                            <p><i class="fas fa-phone mr-2"></i> +****************</p>
                            <p><i class="fas fa-map-marker-alt mr-2"></i> 123 Business St, City, State 12345</p>
                        </div>
                    </div>
                </div>
                
                <div class="border-t border-gray-800 pt-8 text-center">
                    <p class="text-gray-400">
                        &copy; 2024 YourBrand. All rights reserved. | 
                        <a href="#" class="hover:text-white transition-colors">Privacy Policy</a> | 
                        <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
                    </p>
                </div>
            </div>
        </footer>
        
        <!-- Notification System -->
        <div class="fixed top-4 right-4 z-50 space-y-2">
            <div v-for="notification in notifications" :key="notification.id" 
                 :class="[
                     'px-6 py-4 rounded-lg shadow-lg text-white max-w-sm animate-slide-in',
                     notification.type === 'success' ? 'bg-green-500' : 
                     notification.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                 ]">
                <div class="flex items-center justify-between">
                    <span>{{ notification.message }}</span>
                    <button @click="removeNotification(notification.id)" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
</body>
</html>
