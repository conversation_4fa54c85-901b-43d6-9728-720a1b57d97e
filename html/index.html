<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Purpose Template - Home</title>
    <meta name="description" content="A modern, responsive HTML5 template perfect for any business or project">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="../assets/css/tailwind.css" rel="stylesheet">
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans">
    <div id="app">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
            <div class="container-custom">
                <div class="flex justify-between items-center py-4">
                    <!-- Logo -->
                    <div class="flex items-center">
                        <a href="index.html" class="text-2xl font-bold text-gradient">
                            YourBrand
                        </a>
                    </div>
                    
                    <!-- Desktop Navigation -->
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors">Home</a>
                        <a href="about.html" class="text-gray-700 hover:text-blue-600 transition-colors">About</a>
                        <a href="services.html" class="text-gray-700 hover:text-blue-600 transition-colors">Services</a>
                        <a href="portfolio.html" class="text-gray-700 hover:text-blue-600 transition-colors">Portfolio</a>
                        <a href="blog.html" class="text-gray-700 hover:text-blue-600 transition-colors">Blog</a>
                        <a href="pricing.html" class="text-gray-700 hover:text-blue-600 transition-colors">Pricing</a>
                        <a href="contact.html" class="text-gray-700 hover:text-blue-600 transition-colors">Contact</a>
                        <a href="../admin/login.html" class="btn-primary">Admin</a>
                    </div>
                    
                    <!-- Mobile Menu Button -->
                    <button @click="toggleMobileMenu" class="md:hidden mobile-menu-button">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
                
                <!-- Mobile Navigation -->
                <div v-show="mobileMenuOpen" class="md:hidden mobile-menu bg-white border-t border-gray-200 py-4">
                    <div class="flex flex-col space-y-4">
                        <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Home</a>
                        <a href="about.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">About</a>
                        <a href="services.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Services</a>
                        <a href="portfolio.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Portfolio</a>
                        <a href="blog.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Blog</a>
                        <a href="pricing.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Pricing</a>
                        <a href="contact.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Contact</a>
                        <a href="../admin/login.html" class="btn-primary mx-4 text-center">Admin</a>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Hero Section -->
        <section class="bg-gradient-to-br from-blue-50 to-purple-50 pt-20">
            <div class="container-custom section-padding">
                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <div class="animate-fade-in">
                        <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
                            Build Amazing 
                            <span class="text-gradient">Projects</span>
                        </h1>
                        <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                            A modern, responsive template that adapts to any business need. 
                            Perfect for startups, agencies, and enterprises looking to make an impact.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4">
                            <button @click="scrollToSection('features')" class="btn-primary text-lg px-8 py-3">
                                Get Started
                            </button>
                            <button @click="scrollToSection('about')" class="btn-outline text-lg px-8 py-3">
                                Learn More
                            </button>
                        </div>
                    </div>
                    <div class="animate-slide-in">
                        <img src="https://via.placeholder.com/600x400/3B82F6/FFFFFF?text=Hero+Image" alt="Hero Image"
                             class="w-full h-auto rounded-lg shadow-custom-lg">
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Features Section -->
        <section id="features" class="section-padding bg-white">
            <div class="container-custom">
                <div class="text-center mb-16">
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                        Why Choose Our Template?
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Built with modern technologies and best practices to ensure your project succeeds
                    </p>
                </div>
                
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="card text-center hover:shadow-custom-lg transition-shadow">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-mobile-alt text-2xl text-blue-600"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-3">Fully Responsive</h3>
                        <p class="text-gray-600">Looks perfect on all devices, from mobile phones to desktop computers</p>
                    </div>
                    
                    <div class="card text-center hover:shadow-custom-lg transition-shadow">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-code text-2xl text-green-600"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-3">Clean Code</h3>
                        <p class="text-gray-600">Well-structured, commented code that's easy to customize and maintain</p>
                    </div>
                    
                    <div class="card text-center hover:shadow-custom-lg transition-shadow">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-rocket text-2xl text-purple-600"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-3">Fast Performance</h3>
                        <p class="text-gray-600">Optimized for speed with modern CSS and JavaScript frameworks</p>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- CTA Section -->
        <section class="section-padding bg-gradient-to-r from-blue-600 to-purple-600">
            <div class="container-custom text-center">
                <h2 class="text-3xl lg:text-4xl font-bold text-white mb-4">
                    Ready to Get Started?
                </h2>
                <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                    Join thousands of satisfied customers who have built amazing projects with our template
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="contact.html" class="bg-white text-blue-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors">
                        Contact Us
                    </a>
                    <a href="pricing.html" class="border-2 border-white text-white hover:bg-white hover:text-blue-600 font-medium py-3 px-8 rounded-lg transition-all">
                        View Pricing
                    </a>
                </div>
            </div>
        </section>
        
        <!-- Footer -->
        <footer class="bg-gray-900 text-white section-padding">
            <div class="container-custom">
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
                    <div>
                        <h3 class="text-xl font-bold mb-4 text-gradient">YourBrand</h3>
                        <p class="text-gray-400 mb-4">
                            Building amazing digital experiences with modern technology and creative design.
                        </p>
                        <div class="flex space-x-4">
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                        <ul class="space-y-2">
                            <li><a href="about.html" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                            <li><a href="services.html" class="text-gray-400 hover:text-white transition-colors">Services</a></li>
                            <li><a href="portfolio.html" class="text-gray-400 hover:text-white transition-colors">Portfolio</a></li>
                            <li><a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Services</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Web Design</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Development</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">SEO</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Consulting</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Contact Info</h4>
                        <div class="space-y-2 text-gray-400">
                            <p><i class="fas fa-envelope mr-2"></i> <EMAIL></p>
                            <p><i class="fas fa-phone mr-2"></i> +****************</p>
                            <p><i class="fas fa-map-marker-alt mr-2"></i> 123 Business St, City, State 12345</p>
                        </div>
                    </div>
                </div>
                
                <div class="border-t border-gray-800 pt-8 text-center">
                    <p class="text-gray-400">
                        &copy; 2024 YourBrand. All rights reserved. | 
                        <a href="#" class="hover:text-white transition-colors">Privacy Policy</a> | 
                        <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
                    </p>
                </div>
            </div>
        </footer>
        
        <!-- Notification System -->
        <div class="fixed top-4 right-4 z-50 space-y-2">
            <div v-for="notification in notifications" :key="notification.id" 
                 :class="[
                     'px-6 py-4 rounded-lg shadow-lg text-white max-w-sm animate-slide-in',
                     notification.type === 'success' ? 'bg-green-500' : 
                     notification.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                 ]">
                <div class="flex items-center justify-between">
                    <span>{{ notification.message }}</span>
                    <button @click="removeNotification(notification.id)" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
</body>
</html>
