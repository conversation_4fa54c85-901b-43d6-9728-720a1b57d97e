<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Purpose Template - Home</title>
    <meta name="description" content="A modern, responsive HTML5 template perfect for any business or project">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="../assets/css/tailwind.css" rel="stylesheet">
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans">
    <div id="app">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
            <div class="container-custom">
                <div class="flex justify-between items-center py-4">
                    <!-- Logo -->
                    <div class="flex items-center">
                        <a href="index.html" class="text-2xl font-bold text-gradient">
                            YourBrand
                        </a>
                    </div>
                    
                    <!-- Desktop Navigation -->
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors">Home</a>
                        <a href="about.html" class="text-gray-700 hover:text-blue-600 transition-colors">About</a>
                        <a href="services.html" class="text-gray-700 hover:text-blue-600 transition-colors">Services</a>
                        <a href="portfolio.html" class="text-gray-700 hover:text-blue-600 transition-colors">Portfolio</a>
                        <a href="blog.html" class="text-gray-700 hover:text-blue-600 transition-colors">Blog</a>
                        <a href="pricing.html" class="text-gray-700 hover:text-blue-600 transition-colors">Pricing</a>
                        <a href="contact.html" class="text-gray-700 hover:text-blue-600 transition-colors">Contact</a>
                        <a href="../admin/login.html" class="btn-primary">Admin</a>
                    </div>
                    
                    <!-- Mobile Menu Button -->
                    <button @click="toggleMobileMenu" class="md:hidden mobile-menu-button">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
                
                <!-- Mobile Navigation -->
                <div v-show="mobileMenuOpen" class="md:hidden mobile-menu bg-white border-t border-gray-200 py-4">
                    <div class="flex flex-col space-y-4">
                        <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Home</a>
                        <a href="about.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">About</a>
                        <a href="services.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Services</a>
                        <a href="portfolio.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Portfolio</a>
                        <a href="blog.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Blog</a>
                        <a href="pricing.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Pricing</a>
                        <a href="contact.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Contact</a>
                        <a href="../admin/login.html" class="btn-primary mx-4 text-center">Admin</a>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Hero Section -->
        <section class="hero-section pt-20">
            <div class="container-custom section-padding">
                <div class="grid lg:grid-cols-2 gap-16 items-center">
                    <div class="animate-fade-in">
                        <h1 class="text-5xl lg:text-7xl font-bold text-gray-900 mb-8 leading-tight">
                            Build the future with
                            <span class="text-gradient">modern tools</span>
                        </h1>
                        <p class="text-xl text-gray-600 mb-10 leading-relaxed max-w-lg">
                            Everything you need to create exceptional digital experiences.
                            Trusted by teams worldwide to deliver results that matter.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4">
                            <button @click="scrollToSection('features')" class="btn-primary text-lg px-8 py-4 google-shadow hover:google-shadow-hover">
                                <i class="fas fa-rocket mr-2"></i>
                                Get started for free
                            </button>
                            <button @click="scrollToSection('about')" class="btn-secondary text-lg px-8 py-4">
                                <i class="fas fa-play mr-2"></i>
                                See how it works
                            </button>
                        </div>
                        <div class="mt-8 flex items-center space-x-6 text-sm text-gray-500">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                No credit card required
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-users text-blue-500 mr-2"></i>
                                Join 10,000+ teams
                            </div>
                        </div>
                    </div>
                    <div class="animate-slide-in">
                        <div class="relative">
                            <img src="https://via.placeholder.com/600x400/F8FAFC/4F46E5?text=Dashboard+Preview" alt="Product Preview"
                                 class="w-full h-auto rounded-xl google-shadow-hover">
                            <div class="absolute -bottom-4 -left-4 bg-white rounded-lg p-4 google-shadow">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                    <span class="text-sm font-medium text-gray-700">Live collaboration</span>
                                </div>
                            </div>
                            <div class="absolute -top-4 -right-4 bg-white rounded-lg p-4 google-shadow">
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-chart-line text-blue-500"></i>
                                    <span class="text-sm font-medium text-gray-700">Real-time analytics</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Features Section -->
        <section id="features" class="section-padding bg-gray-50">
            <div class="container-custom">
                <div class="text-center mb-20">
                    <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        Everything you need to succeed
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        Powerful tools and features designed to help you build, launch, and scale your projects with confidence
                    </p>
                </div>

                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="card-elevated text-center group">
                        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200">
                            <i class="fas fa-mobile-alt text-3xl text-white"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Mobile-first design</h3>
                        <p class="text-gray-600 leading-relaxed">Beautiful, responsive layouts that work perfectly on every device and screen size</p>
                    </div>

                    <div class="card-elevated text-center group">
                        <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200">
                            <i class="fas fa-code text-3xl text-white"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Developer friendly</h3>
                        <p class="text-gray-600 leading-relaxed">Clean, well-documented code built with modern frameworks and best practices</p>
                    </div>

                    <div class="card-elevated text-center group">
                        <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200">
                            <i class="fas fa-rocket text-3xl text-white"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Lightning fast</h3>
                        <p class="text-gray-600 leading-relaxed">Optimized for performance with fast loading times and smooth interactions</p>
                    </div>

                    <div class="card-elevated text-center group">
                        <div class="w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200">
                            <i class="fas fa-shield-alt text-3xl text-white"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Secure by default</h3>
                        <p class="text-gray-600 leading-relaxed">Built with security best practices and regular updates to keep your data safe</p>
                    </div>

                    <div class="card-elevated text-center group">
                        <div class="w-20 h-20 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200">
                            <i class="fas fa-users text-3xl text-white"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Team collaboration</h3>
                        <p class="text-gray-600 leading-relaxed">Work together seamlessly with built-in collaboration tools and real-time updates</p>
                    </div>

                    <div class="card-elevated text-center group">
                        <div class="w-20 h-20 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200">
                            <i class="fas fa-chart-line text-3xl text-white"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Advanced analytics</h3>
                        <p class="text-gray-600 leading-relaxed">Detailed insights and reporting to help you make data-driven decisions</p>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- CTA Section -->
        <section class="section-padding bg-gradient-to-r from-blue-600 to-purple-600">
            <div class="container-custom text-center">
                <h2 class="text-3xl lg:text-4xl font-bold text-white mb-4">
                    Ready to Get Started?
                </h2>
                <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                    Join thousands of satisfied customers who have built amazing projects with our template
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="contact.html" class="bg-white text-blue-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors">
                        Contact Us
                    </a>
                    <a href="pricing.html" class="border-2 border-white text-white hover:bg-white hover:text-blue-600 font-medium py-3 px-8 rounded-lg transition-all">
                        View Pricing
                    </a>
                </div>
            </div>
        </section>
        
        <!-- Footer -->
        <footer class="bg-gray-900 text-white section-padding">
            <div class="container-custom">
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
                    <div>
                        <h3 class="text-xl font-bold mb-4 text-gradient">YourBrand</h3>
                        <p class="text-gray-400 mb-4">
                            Building amazing digital experiences with modern technology and creative design.
                        </p>
                        <div class="flex space-x-4">
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                        <ul class="space-y-2">
                            <li><a href="about.html" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                            <li><a href="services.html" class="text-gray-400 hover:text-white transition-colors">Services</a></li>
                            <li><a href="portfolio.html" class="text-gray-400 hover:text-white transition-colors">Portfolio</a></li>
                            <li><a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Services</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Web Design</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Development</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">SEO</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Consulting</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Contact Info</h4>
                        <div class="space-y-2 text-gray-400">
                            <p><i class="fas fa-envelope mr-2"></i> <EMAIL></p>
                            <p><i class="fas fa-phone mr-2"></i> +****************</p>
                            <p><i class="fas fa-map-marker-alt mr-2"></i> 123 Business St, City, State 12345</p>
                        </div>
                    </div>
                </div>
                
                <div class="border-t border-gray-800 pt-8 text-center">
                    <p class="text-gray-400">
                        &copy; 2024 YourBrand. All rights reserved. | 
                        <a href="#" class="hover:text-white transition-colors">Privacy Policy</a> | 
                        <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
                    </p>
                </div>
            </div>
        </footer>
        
        <!-- Notification System -->
        <div class="fixed top-4 right-4 z-50 space-y-2">
            <div v-for="notification in notifications" :key="notification.id" 
                 :class="[
                     'px-6 py-4 rounded-lg shadow-lg text-white max-w-sm animate-slide-in',
                     notification.type === 'success' ? 'bg-green-500' : 
                     notification.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                 ]">
                <div class="flex items-center justify-between">
                    <span>{{ notification.message }}</span>
                    <button @click="removeNotification(notification.id)" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
</body>
</html>
