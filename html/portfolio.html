<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio - Multi-Purpose Template</title>
    <meta name="description" content="Explore our portfolio of successful projects and see how we've helped businesses achieve their goals">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans">
    <div id="app">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
            <div class="container-custom">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <a href="index.html" class="text-2xl font-bold text-gradient">YourBrand</a>
                    </div>
                    
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors">Home</a>
                        <a href="about.html" class="text-gray-700 hover:text-blue-600 transition-colors">About</a>
                        <a href="services.html" class="text-gray-700 hover:text-blue-600 transition-colors">Services</a>
                        <a href="portfolio.html" class="text-blue-600 font-medium">Portfolio</a>
                        <a href="blog.html" class="text-gray-700 hover:text-blue-600 transition-colors">Blog</a>
                        <a href="pricing.html" class="text-gray-700 hover:text-blue-600 transition-colors">Pricing</a>
                        <a href="contact.html" class="text-gray-700 hover:text-blue-600 transition-colors">Contact</a>
                        <a href="../admin/login.html" class="btn-primary">Admin</a>
                    </div>
                    
                    <button @click="toggleMobileMenu" class="md:hidden mobile-menu-button">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
                
                <div v-show="mobileMenuOpen" class="md:hidden mobile-menu bg-white border-t border-gray-200 py-4">
                    <div class="flex flex-col space-y-4">
                        <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Home</a>
                        <a href="about.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">About</a>
                        <a href="services.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Services</a>
                        <a href="portfolio.html" class="text-blue-600 font-medium px-4">Portfolio</a>
                        <a href="blog.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Blog</a>
                        <a href="pricing.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Pricing</a>
                        <a href="contact.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Contact</a>
                        <a href="../admin/login.html" class="btn-primary mx-4 text-center">Admin</a>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Page Header -->
        <section class="bg-gradient-to-br from-blue-50 to-purple-50 pt-20">
            <div class="container-custom section-padding">
                <div class="text-center">
                    <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">Our Portfolio</h1>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                        Discover the amazing projects we've created for our clients across various industries
                    </p>
                    
                    <!-- Filter Buttons -->
                    <div class="flex flex-wrap justify-center gap-4">
                        <button @click="filterProjects('all')" 
                                :class="['px-6 py-2 rounded-full font-medium transition-colors', 
                                        activeFilter === 'all' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100']">
                            All Projects
                        </button>
                        <button @click="filterProjects('web-design')" 
                                :class="['px-6 py-2 rounded-full font-medium transition-colors', 
                                        activeFilter === 'web-design' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100']">
                            Web Design
                        </button>
                        <button @click="filterProjects('development')" 
                                :class="['px-6 py-2 rounded-full font-medium transition-colors', 
                                        activeFilter === 'development' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100']">
                            Development
                        </button>
                        <button @click="filterProjects('ecommerce')" 
                                :class="['px-6 py-2 rounded-full font-medium transition-colors', 
                                        activeFilter === 'ecommerce' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100']">
                            E-commerce
                        </button>
                        <button @click="filterProjects('branding')" 
                                :class="['px-6 py-2 rounded-full font-medium transition-colors', 
                                        activeFilter === 'branding' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100']">
                            Branding
                        </button>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Portfolio Grid -->
        <section class="section-padding bg-white">
            <div class="container-custom">
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Project 1 -->
                    <div v-show="showProject('web-design')" class="group cursor-pointer" @click="openModal">
                        <div class="relative overflow-hidden rounded-lg shadow-custom hover:shadow-custom-lg transition-shadow">
                            <img src="../assets/images/portfolio-1.jpg" alt="TechCorp Website" 
                                 class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                                 onerror="this.src='https://via.placeholder.com/400x300/3B82F6/FFFFFF?text=TechCorp+Website'">
                            <div class="absolute inset-0 bg-blue-600 bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center">
                                <div class="text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <i class="fas fa-search-plus text-3xl mb-2"></i>
                                    <p class="font-medium">View Project</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">TechCorp Website</h3>
                            <p class="text-gray-600 mb-2">Modern corporate website with clean design and user-friendly interface</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">Web Design</span>
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">Responsive</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Project 2 -->
                    <div v-show="showProject('ecommerce')" class="group cursor-pointer" @click="openModal">
                        <div class="relative overflow-hidden rounded-lg shadow-custom hover:shadow-custom-lg transition-shadow">
                            <img src="../assets/images/portfolio-2.jpg" alt="Fashion Store" 
                                 class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                                 onerror="this.src='https://via.placeholder.com/400x300/10B981/FFFFFF?text=Fashion+Store'">
                            <div class="absolute inset-0 bg-green-600 bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center">
                                <div class="text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <i class="fas fa-search-plus text-3xl mb-2"></i>
                                    <p class="font-medium">View Project</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Fashion E-commerce</h3>
                            <p class="text-gray-600 mb-2">Complete online store with payment integration and inventory management</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full">E-commerce</span>
                                <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">Payment Gateway</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Project 3 -->
                    <div v-show="showProject('development')" class="group cursor-pointer" @click="openModal">
                        <div class="relative overflow-hidden rounded-lg shadow-custom hover:shadow-custom-lg transition-shadow">
                            <img src="../assets/images/portfolio-3.jpg" alt="SaaS Dashboard" 
                                 class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                                 onerror="this.src='https://via.placeholder.com/400x300/8B5CF6/FFFFFF?text=SaaS+Dashboard'">
                            <div class="absolute inset-0 bg-purple-600 bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center">
                                <div class="text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <i class="fas fa-search-plus text-3xl mb-2"></i>
                                    <p class="font-medium">View Project</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">SaaS Dashboard</h3>
                            <p class="text-gray-600 mb-2">Complex web application with real-time data and analytics</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">Development</span>
                                <span class="px-3 py-1 bg-red-100 text-red-800 text-sm rounded-full">Real-time</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Project 4 -->
                    <div v-show="showProject('branding')" class="group cursor-pointer" @click="openModal">
                        <div class="relative overflow-hidden rounded-lg shadow-custom hover:shadow-custom-lg transition-shadow">
                            <img src="../assets/images/portfolio-4.jpg" alt="Restaurant Brand" 
                                 class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                                 onerror="this.src='https://via.placeholder.com/400x300/F59E0B/FFFFFF?text=Restaurant+Brand'">
                            <div class="absolute inset-0 bg-yellow-600 bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center">
                                <div class="text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <i class="fas fa-search-plus text-3xl mb-2"></i>
                                    <p class="font-medium">View Project</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Restaurant Branding</h3>
                            <p class="text-gray-600 mb-2">Complete brand identity and website for local restaurant chain</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-orange-100 text-orange-800 text-sm rounded-full">Branding</span>
                                <span class="px-3 py-1 bg-pink-100 text-pink-800 text-sm rounded-full">Logo Design</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Project 5 -->
                    <div v-show="showProject('web-design')" class="group cursor-pointer" @click="openModal">
                        <div class="relative overflow-hidden rounded-lg shadow-custom hover:shadow-custom-lg transition-shadow">
                            <img src="../assets/images/portfolio-5.jpg" alt="Healthcare Portal" 
                                 class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                                 onerror="this.src='https://via.placeholder.com/400x300/EF4444/FFFFFF?text=Healthcare+Portal'">
                            <div class="absolute inset-0 bg-red-600 bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center">
                                <div class="text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <i class="fas fa-search-plus text-3xl mb-2"></i>
                                    <p class="font-medium">View Project</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Healthcare Portal</h3>
                            <p class="text-gray-600 mb-2">Patient management system with appointment booking and records</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">Web Design</span>
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">Healthcare</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Project 6 -->
                    <div v-show="showProject('development')" class="group cursor-pointer" @click="openModal">
                        <div class="relative overflow-hidden rounded-lg shadow-custom hover:shadow-custom-lg transition-shadow">
                            <img src="../assets/images/portfolio-6.jpg" alt="Mobile App" 
                                 class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                                 onerror="this.src='https://via.placeholder.com/400x300/6366F1/FFFFFF?text=Mobile+App'">
                            <div class="absolute inset-0 bg-indigo-600 bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center">
                                <div class="text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <i class="fas fa-search-plus text-3xl mb-2"></i>
                                    <p class="font-medium">View Project</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Fitness Mobile App</h3>
                            <p class="text-gray-600 mb-2">Cross-platform mobile application for fitness tracking and coaching</p>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-indigo-100 text-indigo-800 text-sm rounded-full">Development</span>
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">Mobile</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- CTA Section -->
        <section class="section-padding bg-gradient-to-r from-blue-600 to-purple-600">
            <div class="container-custom text-center">
                <h2 class="text-3xl lg:text-4xl font-bold text-white mb-4">
                    Ready to Start Your Project?
                </h2>
                <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                    Let's create something amazing together. Contact us to discuss your project needs.
                </p>
                <a href="contact.html" class="bg-white text-blue-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors inline-block">
                    Start Your Project
                </a>
            </div>
        </section>
        
        <!-- Footer -->
        <footer class="bg-gray-900 text-white section-padding">
            <div class="container-custom">
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
                    <div>
                        <h3 class="text-xl font-bold mb-4 text-gradient">YourBrand</h3>
                        <p class="text-gray-400 mb-4">
                            Building amazing digital experiences with modern technology and creative design.
                        </p>
                        <div class="flex space-x-4">
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                        <ul class="space-y-2">
                            <li><a href="about.html" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                            <li><a href="services.html" class="text-gray-400 hover:text-white transition-colors">Services</a></li>
                            <li><a href="portfolio.html" class="text-gray-400 hover:text-white transition-colors">Portfolio</a></li>
                            <li><a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Services</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Web Design</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Development</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">SEO</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Consulting</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Contact Info</h4>
                        <div class="space-y-2 text-gray-400">
                            <p><i class="fas fa-envelope mr-2"></i> <EMAIL></p>
                            <p><i class="fas fa-phone mr-2"></i> +****************</p>
                            <p><i class="fas fa-map-marker-alt mr-2"></i> 123 Business St, City, State 12345</p>
                        </div>
                    </div>
                </div>
                
                <div class="border-t border-gray-800 pt-8 text-center">
                    <p class="text-gray-400">
                        &copy; 2024 YourBrand. All rights reserved. | 
                        <a href="#" class="hover:text-white transition-colors">Privacy Policy</a> | 
                        <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
                    </p>
                </div>
            </div>
        </footer>
        
        <!-- Project Modal -->
        <div v-show="modalOpen" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4" @click="closeModal">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto" @click.stop>
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-2xl font-bold text-gray-900">Project Details</h3>
                        <button @click="closeModal" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    
                    <div class="grid lg:grid-cols-2 gap-8">
                        <div>
                            <img src="../assets/images/portfolio-1.jpg" alt="Project" 
                                 class="w-full h-64 object-cover rounded-lg"
                                 onerror="this.src='https://via.placeholder.com/600x400/3B82F6/FFFFFF?text=Project+Image'">
                        </div>
                        <div>
                            <h4 class="text-xl font-semibold text-gray-900 mb-4">TechCorp Website</h4>
                            <p class="text-gray-600 mb-6">
                                A comprehensive corporate website redesign that modernized the company's online presence 
                                and improved user engagement by 150%. The project included responsive design, 
                                SEO optimization, and integration with their existing CRM system.
                            </p>
                            
                            <div class="space-y-4">
                                <div>
                                    <h5 class="font-semibold text-gray-900 mb-2">Technologies Used:</h5>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">HTML5</span>
                                        <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">CSS3</span>
                                        <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">JavaScript</span>
                                        <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full">Vue.js</span>
                                    </div>
                                </div>
                                
                                <div>
                                    <h5 class="font-semibold text-gray-900 mb-2">Project Duration:</h5>
                                    <p class="text-gray-600">6 weeks</p>
                                </div>
                                
                                <div>
                                    <h5 class="font-semibold text-gray-900 mb-2">Client:</h5>
                                    <p class="text-gray-600">TechCorp Solutions</p>
                                </div>
                            </div>
                            
                            <div class="mt-6">
                                <a href="#" class="btn-primary inline-block">View Live Site</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Notification System -->
        <div class="fixed top-4 right-4 z-50 space-y-2">
            <div v-for="notification in notifications" :key="notification.id" 
                 :class="[
                     'px-6 py-4 rounded-lg shadow-lg text-white max-w-sm animate-slide-in',
                     notification.type === 'success' ? 'bg-green-500' : 
                     notification.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                 ]">
                <div class="flex items-center justify-between">
                    <span>{{ notification.message }}</span>
                    <button @click="removeNotification(notification.id)" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script>
        // Portfolio-specific Vue app
        const { createApp } = Vue;
        
        const app = createApp({
            data() {
                return {
                    activeFilter: 'all',
                    mobileMenuOpen: false,
                    modalOpen: false,
                    notifications: []
                }
            },
            
            methods: {
                filterProjects(category) {
                    this.activeFilter = category;
                },
                
                showProject(category) {
                    return this.activeFilter === 'all' || this.activeFilter === category;
                },
                
                toggleMobileMenu() {
                    this.mobileMenuOpen = !this.mobileMenuOpen;
                },
                
                openModal() {
                    this.modalOpen = true;
                    document.body.style.overflow = 'hidden';
                },
                
                closeModal() {
                    this.modalOpen = false;
                    document.body.style.overflow = 'auto';
                },
                
                showNotification(message, type = 'info') {
                    const notification = {
                        id: Date.now(),
                        message,
                        type
                    };
                    
                    this.notifications.push(notification);
                    
                    setTimeout(() => {
                        this.removeNotification(notification.id);
                    }, 5000);
                },
                
                removeNotification(id) {
                    this.notifications = this.notifications.filter(n => n.id !== id);
                }
            }
        });
        
        app.mount('#app');
    </script>
</body>
</html>
