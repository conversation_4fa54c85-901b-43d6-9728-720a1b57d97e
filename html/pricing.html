<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing Plans - Multi-Purpose Template</title>
    <meta name="description" content="Choose the perfect plan for your business needs with our flexible pricing options">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="../assets/css/tailwind.css" rel="stylesheet">
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans">
    <div id="app">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
            <div class="container-custom">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <a href="index.html" class="text-2xl font-bold text-gradient">YourBrand</a>
                    </div>
                    
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors">Home</a>
                        <a href="about.html" class="text-gray-700 hover:text-blue-600 transition-colors">About</a>
                        <a href="services.html" class="text-gray-700 hover:text-blue-600 transition-colors">Services</a>
                        <a href="portfolio.html" class="text-gray-700 hover:text-blue-600 transition-colors">Portfolio</a>
                        <a href="blog.html" class="text-gray-700 hover:text-blue-600 transition-colors">Blog</a>
                        <a href="pricing.html" class="text-blue-600 font-medium">Pricing</a>
                        <a href="contact.html" class="text-gray-700 hover:text-blue-600 transition-colors">Contact</a>
                        <a href="../admin/login.html" class="btn-primary">Admin</a>
                    </div>
                    
                    <button @click="toggleMobileMenu" class="md:hidden mobile-menu-button">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
                
                <div v-show="mobileMenuOpen" class="md:hidden mobile-menu bg-white border-t border-gray-200 py-4">
                    <div class="flex flex-col space-y-4">
                        <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Home</a>
                        <a href="about.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">About</a>
                        <a href="services.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Services</a>
                        <a href="portfolio.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Portfolio</a>
                        <a href="blog.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Blog</a>
                        <a href="pricing.html" class="text-blue-600 font-medium px-4">Pricing</a>
                        <a href="contact.html" class="text-gray-700 hover:text-blue-600 transition-colors px-4">Contact</a>
                        <a href="../admin/login.html" class="btn-primary mx-4 text-center">Admin</a>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Page Header -->
        <section class="hero-section pt-20">
            <div class="container-custom section-padding">
                <div class="text-center">
                    <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">Choose your plan</h1>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-12 leading-relaxed">
                        Simple, transparent pricing that grows with you. Start free and upgrade as your team scales.
                    </p>
                    
                    <!-- Billing Toggle -->
                    <div class="flex items-center justify-center space-x-4">
                        <span class="text-gray-600">Monthly</span>
                        <button @click="toggleBilling" class="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" :class="{ 'bg-blue-600': isYearly }">
                            <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform" :class="{ 'translate-x-6': isYearly, 'translate-x-1': !isYearly }"></span>
                        </button>
                        <span class="text-gray-600">Yearly <span class="text-green-600 text-sm font-medium">(Save 20%)</span></span>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Pricing Cards -->
        <section class="section-padding bg-white">
            <div class="container-custom">
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Starter Plan -->
                    <div class="card hover:shadow-custom-lg transition-shadow">
                        <div class="text-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Starter</h3>
                            <p class="text-gray-600 mb-4">Perfect for small businesses getting started</p>
                            <div class="text-4xl font-bold text-gray-900">
                                ${{ isYearly ? '19' : '29' }}
                                <span class="text-lg font-normal text-gray-600">/month</span>
                            </div>
                            <p v-if="isYearly" class="text-sm text-green-600 mt-1">Billed annually ($228/year)</p>
                        </div>
                        
                        <ul class="space-y-3 mb-8">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Up to 5 pages</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Basic SEO optimization</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Mobile responsive design</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Contact form integration</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Email support</span>
                            </li>
                        </ul>
                        
                        <button class="btn-outline w-full">Get Started</button>
                    </div>
                    
                    <!-- Professional Plan (Popular) -->
                    <div class="card hover:shadow-custom-lg transition-shadow relative border-2 border-blue-500">
                        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                            <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">Most Popular</span>
                        </div>
                        
                        <div class="text-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Professional</h3>
                            <p class="text-gray-600 mb-4">Ideal for growing businesses</p>
                            <div class="text-4xl font-bold text-gray-900">
                                ${{ isYearly ? '39' : '59' }}
                                <span class="text-lg font-normal text-gray-600">/month</span>
                            </div>
                            <p v-if="isYearly" class="text-sm text-green-600 mt-1">Billed annually ($468/year)</p>
                        </div>
                        
                        <ul class="space-y-3 mb-8">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Up to 15 pages</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Advanced SEO optimization</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">E-commerce integration</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Analytics dashboard</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Priority support</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Social media integration</span>
                            </li>
                        </ul>
                        
                        <button class="btn-primary w-full">Get Started</button>
                    </div>
                    
                    <!-- Enterprise Plan -->
                    <div class="card hover:shadow-custom-lg transition-shadow">
                        <div class="text-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Enterprise</h3>
                            <p class="text-gray-600 mb-4">For large organizations with custom needs</p>
                            <div class="text-4xl font-bold text-gray-900">
                                ${{ isYearly ? '79' : '99' }}
                                <span class="text-lg font-normal text-gray-600">/month</span>
                            </div>
                            <p v-if="isYearly" class="text-sm text-green-600 mt-1">Billed annually ($948/year)</p>
                        </div>
                        
                        <ul class="space-y-3 mb-8">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Unlimited pages</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Custom development</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Advanced integrations</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Dedicated account manager</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">24/7 phone support</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">Custom training</span>
                            </li>
                        </ul>
                        
                        <button class="btn-outline w-full">Contact Sales</button>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- FAQ Section -->
        <section class="section-padding bg-gray-50">
            <div class="container-custom">
                <div class="text-center mb-12">
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
                    <p class="text-xl text-gray-600">Everything you need to know about our pricing</p>
                </div>
                
                <div class="max-w-3xl mx-auto space-y-4">
                    <div class="card">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Can I change my plan later?</h3>
                        <p class="text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.</p>
                    </div>
                    
                    <div class="card">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Is there a setup fee?</h3>
                        <p class="text-gray-600">No, there are no setup fees for any of our plans. You only pay the monthly or annual subscription fee.</p>
                    </div>
                    
                    <div class="card">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Do you offer refunds?</h3>
                        <p class="text-gray-600">We offer a 30-day money-back guarantee for all new subscriptions. If you're not satisfied, we'll refund your payment.</p>
                    </div>
                    
                    <div class="card">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">What payment methods do you accept?</h3>
                        <p class="text-gray-600">We accept all major credit cards, PayPal, and bank transfers for annual plans.</p>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- CTA Section -->
        <section class="section-padding bg-gradient-to-r from-blue-600 to-purple-600">
            <div class="container-custom text-center">
                <h2 class="text-3xl lg:text-4xl font-bold text-white mb-4">
                    Ready to Get Started?
                </h2>
                <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                    Join thousands of satisfied customers who have chosen our platform
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="contact.html" class="bg-white text-blue-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors">
                        Start Free Trial
                    </a>
                    <a href="contact.html" class="border-2 border-white text-white hover:bg-white hover:text-blue-600 font-medium py-3 px-8 rounded-lg transition-all">
                        Contact Sales
                    </a>
                </div>
            </div>
        </section>
        
        <!-- Footer -->
        <footer class="bg-gray-900 text-white section-padding">
            <div class="container-custom">
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
                    <div>
                        <h3 class="text-xl font-bold mb-4 text-gradient">YourBrand</h3>
                        <p class="text-gray-400 mb-4">
                            Building amazing digital experiences with modern technology and creative design.
                        </p>
                        <div class="flex space-x-4">
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                        <ul class="space-y-2">
                            <li><a href="about.html" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                            <li><a href="services.html" class="text-gray-400 hover:text-white transition-colors">Services</a></li>
                            <li><a href="portfolio.html" class="text-gray-400 hover:text-white transition-colors">Portfolio</a></li>
                            <li><a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Services</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Web Design</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Development</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">SEO</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Consulting</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Contact Info</h4>
                        <div class="space-y-2 text-gray-400">
                            <p><i class="fas fa-envelope mr-2"></i> <EMAIL></p>
                            <p><i class="fas fa-phone mr-2"></i> +****************</p>
                            <p><i class="fas fa-map-marker-alt mr-2"></i> 123 Business St, City, State 12345</p>
                        </div>
                    </div>
                </div>
                
                <div class="border-t border-gray-800 pt-8 text-center">
                    <p class="text-gray-400">
                        &copy; 2024 YourBrand. All rights reserved. | 
                        <a href="#" class="hover:text-white transition-colors">Privacy Policy</a> | 
                        <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
                    </p>
                </div>
            </div>
        </footer>
        
        <!-- Notification System -->
        <div class="fixed top-4 right-4 z-50 space-y-2">
            <div v-for="notification in notifications" :key="notification.id" 
                 :class="[
                     'px-6 py-4 rounded-lg shadow-lg text-white max-w-sm animate-slide-in',
                     notification.type === 'success' ? 'bg-green-500' : 
                     notification.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                 ]">
                <div class="flex items-center justify-between">
                    <span>{{ notification.message }}</span>
                    <button @click="removeNotification(notification.id)" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script>
        // Extend the main Vue app with pricing-specific functionality
        const { createApp } = Vue;
        
        const app = createApp({
            data() {
                return {
                    isYearly: false,
                    mobileMenuOpen: false,
                    notifications: []
                }
            },
            
            methods: {
                toggleBilling() {
                    this.isYearly = !this.isYearly;
                },
                
                toggleMobileMenu() {
                    this.mobileMenuOpen = !this.mobileMenuOpen;
                },
                
                showNotification(message, type = 'info') {
                    const notification = {
                        id: Date.now(),
                        message,
                        type
                    };
                    
                    this.notifications.push(notification);
                    
                    setTimeout(() => {
                        this.removeNotification(notification.id);
                    }, 5000);
                },
                
                removeNotification(id) {
                    this.notifications = this.notifications.filter(n => n.id !== id);
                }
            }
        });
        
        app.mount('#app');
    </script>
</body>
</html>
