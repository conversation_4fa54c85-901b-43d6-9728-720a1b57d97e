@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Open Sans', 'Roboto', sans-serif;
  }
}

@layer components {
  /* Custom component styles */
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-outline {
    @apply border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }
  
  .section-padding {
    @apply py-16 px-4 sm:px-6 lg:px-8;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }
  
  /* Admin Dashboard Styles */
  .admin-sidebar {
    @apply bg-gray-900 text-white w-64 min-h-screen fixed left-0 top-0 transform transition-transform duration-300 ease-in-out z-30;
  }
  
  .admin-main {
    @apply ml-64 min-h-screen bg-gray-50;
  }
  
  .admin-header {
    @apply bg-white shadow-sm border-b border-gray-200 px-6 py-4;
  }
  
  .admin-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .data-table {
    @apply w-full bg-white rounded-lg shadow-sm overflow-hidden;
  }
  
  .data-table th {
    @apply bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .data-table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200;
  }

  /* Chart container styles */
  .chart-container {
    @apply relative w-full;
    height: 16rem; /* 256px */
    max-height: 16rem;
  }

  .chart-container canvas {
    @apply absolute top-0 left-0 w-full h-full;
    max-width: 100% !important;
    max-height: 100% !important;
  }
  
  /* Mobile responsive admin */
  @media (max-width: 768px) {
    .admin-sidebar {
      @apply -translate-x-full;
    }
    
    .admin-sidebar.open {
      @apply translate-x-0;
    }
    
    .admin-main {
      @apply ml-0;
    }
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .backdrop-blur-custom {
    backdrop-filter: blur(10px);
  }
}
