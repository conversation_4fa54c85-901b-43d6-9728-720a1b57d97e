<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Management - Admin Dashboard</title>
    <meta name="description" content="Manage website content, blog posts, and pages">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans bg-gray-50">
    <div id="app">
        <!-- Admin Sidebar -->
        <aside :class="['admin-sidebar', { 'open': admin<PERSON>idebar<PERSON><PERSON> }]">
            <div class="p-6">
                <a href="../html/index.html" class="text-2xl font-bold text-white mb-8 block">
                    YourBrand
                </a>
                
                <nav class="space-y-2">
                    <a href="dashboard.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="users.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-users"></i>
                        <span>Users</span>
                    </a>
                    <a href="content.html" class="flex items-center space-x-3 text-white bg-blue-600 px-4 py-3 rounded-lg">
                        <i class="fas fa-file-alt"></i>
                        <span>Content</span>
                    </a>
                    <a href="analytics.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                    <a href="settings.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                    
                    <div class="border-t border-gray-700 my-4"></div>
                    
                    <a href="../html/index.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-external-link-alt"></i>
                        <span>View Website</span>
                    </a>
                    <a href="login.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </nav>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button @click="toggleAdminSidebar" class="md:hidden sidebar-toggle text-gray-600 hover:text-gray-900">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h1 class="text-2xl font-bold text-gray-900">Content Management</h1>
                    </div>
                </div>
            </header>
            
            <!-- Content -->
            <div class="p-6">
                <div class="admin-card">
                    <div class="text-center py-12">
                        <i class="fas fa-file-alt text-6xl text-gray-400 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Content Management System</h3>
                        <p class="text-gray-600 mb-6">
                            Blog posts, pages, and content editing features would be implemented here.
                        </p>
                        <button @click="showNotification('Content management feature coming soon!', 'info')" class="btn-primary">
                            Create New Post
                        </button>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- Notification System -->
        <div class="fixed top-4 right-4 z-50 space-y-2">
            <div v-for="notification in notifications" :key="notification.id" 
                 :class="[
                     'px-6 py-4 rounded-lg shadow-lg text-white max-w-sm animate-slide-in',
                     notification.type === 'success' ? 'bg-green-500' : 
                     notification.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                 ]">
                <div class="flex items-center justify-between">
                    <span>{{ notification.message }}</span>
                    <button @click="removeNotification(notification.id)" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
</body>
</html>
