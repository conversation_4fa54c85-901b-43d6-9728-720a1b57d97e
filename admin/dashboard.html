<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Multi-Purpose Template</title>
    <meta name="description" content="Admin dashboard for managing your website content and settings">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="../assets/css/tailwind.css" rel="stylesheet">
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="font-sans bg-gray-50">
    <div id="app">
        <!-- Admin Sidebar -->
        <aside :class="['admin-sidebar', { 'open': adminSidebarOpen }]">
            <div class="p-6">
                <a href="../html/index.html" class="text-2xl font-bold text-white mb-8 block">
                    YourBrand
                </a>
                
                <nav class="space-y-2">
                    <a href="dashboard.html" class="flex items-center space-x-3 text-white bg-blue-600 px-4 py-3 rounded-lg">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="users.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-users"></i>
                        <span>Users</span>
                    </a>
                    <a href="content.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-file-alt"></i>
                        <span>Content</span>
                    </a>
                    <a href="analytics.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                    <a href="settings.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                    
                    <div class="border-t border-gray-700 my-4"></div>
                    
                    <a href="../html/index.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-external-link-alt"></i>
                        <span>View Website</span>
                    </a>
                    <a href="login.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </nav>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button @click="toggleAdminSidebar" class="md:hidden sidebar-toggle text-gray-600 hover:text-gray-900">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <!-- Search -->
                        <div class="relative hidden md:block">
                            <input 
                                type="text" 
                                placeholder="Search..." 
                                class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                        
                        <!-- Notifications -->
                        <button class="relative text-gray-600 hover:text-gray-900">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                        </button>
                        
                        <!-- User Menu -->
                        <div class="flex items-center space-x-3">
                            <img src="https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A" alt="Admin" 
                                 class="w-8 h-8 rounded-full object-cover">
                            <span class="hidden md:block text-gray-700 font-medium">Admin User</span>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Dashboard Content -->
            <div class="p-6">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="admin-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Total Users</p>
                                <p class="text-3xl font-bold text-gray-900">1,234</p>
                                <p class="text-sm text-green-600">+12% from last month</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-blue-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Page Views</p>
                                <p class="text-3xl font-bold text-gray-900">45,678</p>
                                <p class="text-sm text-green-600">+8% from last month</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-eye text-green-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Revenue</p>
                                <p class="text-3xl font-bold text-gray-900">$12,345</p>
                                <p class="text-sm text-red-600">-3% from last month</p>
                            </div>
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-purple-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Conversion Rate</p>
                                <p class="text-3xl font-bold text-gray-900">3.2%</p>
                                <p class="text-sm text-green-600">+0.5% from last month</p>
                            </div>
                            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-line text-yellow-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts Row -->
                <div class="grid lg:grid-cols-2 gap-6 mb-8">
                    <!-- Traffic Chart -->
                    <div class="admin-card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Website Traffic</h3>
                            <select class="text-sm border border-gray-300 rounded px-3 py-1">
                                <option>Last 7 days</option>
                                <option>Last 30 days</option>
                                <option>Last 90 days</option>
                            </select>
                        </div>
                        <div class="chart-container">
                            <canvas id="trafficChart"></canvas>
                        </div>
                    </div>

                    <!-- Revenue Chart -->
                    <div class="admin-card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Revenue Overview</h3>
                            <select class="text-sm border border-gray-300 rounded px-3 py-1">
                                <option>This year</option>
                                <option>Last year</option>
                            </select>
                        </div>
                        <div class="chart-container">
                            <canvas id="revenueChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity & Quick Actions -->
                <div class="grid lg:grid-cols-2 gap-6">
                    <!-- Recent Activity -->
                    <div class="admin-card">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-blue-600 text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">New user registered</p>
                                    <p class="text-xs text-gray-500">2 minutes ago</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-shopping-cart text-green-600 text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">New order received</p>
                                    <p class="text-xs text-gray-500">15 minutes ago</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-file-alt text-purple-600 text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">Blog post published</p>
                                    <p class="text-xs text-gray-500">1 hour ago</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-cog text-yellow-600 text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">System backup completed</p>
                                    <p class="text-xs text-gray-500">3 hours ago</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <a href="#" class="text-sm text-blue-600 hover:text-blue-500 font-medium">
                                View all activity →
                            </a>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="admin-card">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <button @click="showNotification('Add User functionality would be implemented here', 'info')" 
                                    class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                <i class="fas fa-user-plus text-blue-600 text-2xl mb-2"></i>
                                <span class="text-sm font-medium text-gray-700">Add User</span>
                            </button>
                            
                            <button @click="showNotification('Create Post functionality would be implemented here', 'info')" 
                                    class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                <i class="fas fa-plus text-green-600 text-2xl mb-2"></i>
                                <span class="text-sm font-medium text-gray-700">Create Post</span>
                            </button>
                            
                            <button @click="showNotification('View Reports functionality would be implemented here', 'info')" 
                                    class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                <i class="fas fa-chart-bar text-purple-600 text-2xl mb-2"></i>
                                <span class="text-sm font-medium text-gray-700">View Reports</span>
                            </button>
                            
                            <button @click="showNotification('System Settings functionality would be implemented here', 'info')" 
                                    class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                <i class="fas fa-cog text-gray-600 text-2xl mb-2"></i>
                                <span class="text-sm font-medium text-gray-700">Settings</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- Notification System -->
        <div class="fixed top-4 right-4 z-50 space-y-2">
            <div v-for="notification in notifications" :key="notification.id" 
                 :class="[
                     'px-6 py-4 rounded-lg shadow-lg text-white max-w-sm animate-slide-in',
                     notification.type === 'success' ? 'bg-green-500' : 
                     notification.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                 ]">
                <div class="flex items-center justify-between">
                    <span>{{ notification.message }}</span>
                    <button @click="removeNotification(notification.id)" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
    <script>
        // Initialize charts after page load
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent multiple chart initializations
            if (window.chartsInitialized) return;
            window.chartsInitialized = true;

            // Traffic Chart
            const trafficCanvas = document.getElementById('trafficChart');
            if (trafficCanvas) {
                const trafficCtx = trafficCanvas.getContext('2d');
                window.trafficChart = new Chart(trafficCtx, {
                    type: 'line',
                    data: {
                        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                        datasets: [{
                            label: 'Visitors',
                            data: [1200, 1900, 3000, 5000, 2000, 3000, 4500],
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                grid: {
                                    display: false
                                }
                            },
                            y: {
                                display: true,
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.1)'
                                }
                            }
                        }
                    }
                });
            }

            // Revenue Chart
            const revenueCanvas = document.getElementById('revenueChart');
            if (revenueCanvas) {
                const revenueCtx = revenueCanvas.getContext('2d');
                window.revenueChart = new Chart(revenueCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                        datasets: [{
                            label: 'Revenue',
                            data: [12000, 19000, 15000, 25000, 22000, 30000],
                            backgroundColor: '#10B981',
                            borderRadius: 4,
                            borderSkipped: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                grid: {
                                    display: false
                                }
                            },
                            y: {
                                display: true,
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.1)'
                                }
                            }
                        }
                    }
                });
            }
        });

        // Clean up charts when page unloads
        window.addEventListener('beforeunload', function() {
            if (window.trafficChart) {
                window.trafficChart.destroy();
            }
            if (window.revenueChart) {
                window.revenueChart.destroy();
            }
        });
    </script>
</body>
</html>
