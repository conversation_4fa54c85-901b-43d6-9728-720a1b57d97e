<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Admin Dashboard</title>
    <meta name="description" content="Manage users, roles, and permissions in the admin dashboard">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans bg-gray-50">
    <div id="app">
        <!-- Admin Sidebar -->
        <aside :class="['admin-sidebar', { 'open': adminSidebarOpen }]">
            <div class="p-6">
                <a href="../html/index.html" class="text-2xl font-bold text-white mb-8 block">
                    YourBrand
                </a>
                
                <nav class="space-y-2">
                    <a href="dashboard.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="users.html" class="flex items-center space-x-3 text-white bg-blue-600 px-4 py-3 rounded-lg">
                        <i class="fas fa-users"></i>
                        <span>Users</span>
                    </a>
                    <a href="content.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-file-alt"></i>
                        <span>Content</span>
                    </a>
                    <a href="analytics.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                    <a href="settings.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                    
                    <div class="border-t border-gray-700 my-4"></div>
                    
                    <a href="../html/index.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-external-link-alt"></i>
                        <span>View Website</span>
                    </a>
                    <a href="login.html" class="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </nav>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button @click="toggleAdminSidebar" class="md:hidden sidebar-toggle text-gray-600 hover:text-gray-900">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h1 class="text-2xl font-bold text-gray-900">User Management</h1>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button @click="openAddUserModal" class="btn-primary">
                            <i class="fas fa-plus mr-2"></i>
                            Add User
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- Content -->
            <div class="p-6">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="admin-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Total Users</p>
                                <p class="text-3xl font-bold text-gray-900">{{ users.length }}</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-blue-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Active Users</p>
                                <p class="text-3xl font-bold text-gray-900">{{ activeUsers }}</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-user-check text-green-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Admins</p>
                                <p class="text-3xl font-bold text-gray-900">{{ adminUsers }}</p>
                            </div>
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-user-shield text-purple-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">New This Month</p>
                                <p class="text-3xl font-bold text-gray-900">{{ newUsers }}</p>
                            </div>
                            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-user-plus text-yellow-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters and Search -->
                <div class="admin-card mb-6">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                            <div class="relative">
                                <input 
                                    type="text" 
                                    v-model="searchQuery"
                                    placeholder="Search users..." 
                                    class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                            </div>
                            
                            <select v-model="roleFilter" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">All Roles</option>
                                <option value="admin">Admin</option>
                                <option value="editor">Editor</option>
                                <option value="user">User</option>
                            </select>
                            
                            <select v-model="statusFilter" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="btn-secondary">
                                <i class="fas fa-download mr-2"></i>
                                Export
                            </button>
                            <button class="btn-secondary">
                                <i class="fas fa-filter mr-2"></i>
                                Filter
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Users Table -->
                <div class="admin-card">
                    <div class="overflow-x-auto">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" class="rounded">
                                    </th>
                                    <th>User</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="user in filteredUsers" :key="user.id">
                                    <td>
                                        <input type="checkbox" class="rounded">
                                    </td>
                                    <td>
                                        <div class="flex items-center space-x-3">
                                            <img :src="user.avatar" :alt="user.name" 
                                                 class="w-8 h-8 rounded-full object-cover"
                                                 onerror="this.src='https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=' + user.name.charAt(0)">
                                            <div>
                                                <div class="font-medium text-gray-900">{{ user.name }}</div>
                                                <div class="text-sm text-gray-500">{{ user.username }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ user.email }}</td>
                                    <td>
                                        <span :class="[
                                            'px-2 py-1 text-xs font-medium rounded-full',
                                            user.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                                            user.role === 'editor' ? 'bg-blue-100 text-blue-800' :
                                            'bg-gray-100 text-gray-800'
                                        ]">
                                            {{ user.role.charAt(0).toUpperCase() + user.role.slice(1) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span :class="[
                                            'px-2 py-1 text-xs font-medium rounded-full',
                                            user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                        ]">
                                            {{ user.status.charAt(0).toUpperCase() + user.status.slice(1) }}
                                        </span>
                                    </td>
                                    <td class="text-sm text-gray-500">{{ user.lastLogin }}</td>
                                    <td>
                                        <div class="flex space-x-2">
                                            <button @click="editUser(user)" class="text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button @click="deleteUser(user.id)" class="text-red-600 hover:text-red-800">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="flex items-center justify-between mt-6">
                        <div class="text-sm text-gray-700">
                            Showing {{ (currentPage - 1) * itemsPerPage + 1 }} to {{ Math.min(currentPage * itemsPerPage, filteredUsers.length) }} of {{ filteredUsers.length }} results
                        </div>
                        <div class="flex space-x-2">
                            <button @click="previousPage" :disabled="currentPage === 1" class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50">
                                Previous
                            </button>
                            <button @click="nextPage" :disabled="currentPage >= totalPages" class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50">
                                Next
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- Add/Edit User Modal -->
        <div v-show="userModalOpen" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4" @click="closeUserModal">
            <div class="bg-white rounded-lg max-w-md w-full" @click.stop>
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-bold text-gray-900">{{ editingUser ? 'Edit User' : 'Add New User' }}</h3>
                        <button @click="closeUserModal" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    
                    <form @submit.prevent="saveUser" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                            <input type="text" v-model="userForm.name" class="input-field" required>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                            <input type="text" v-model="userForm.username" class="input-field" required>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" v-model="userForm.email" class="input-field" required>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                            <select v-model="userForm.role" class="input-field" required>
                                <option value="user">User</option>
                                <option value="editor">Editor</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <select v-model="userForm.status" class="input-field" required>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        
                        <div v-if="!editingUser">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                            <input type="password" v-model="userForm.password" class="input-field" required>
                        </div>
                        
                        <div class="flex space-x-4 pt-4">
                            <button type="submit" class="btn-primary flex-1">
                                {{ editingUser ? 'Update User' : 'Add User' }}
                            </button>
                            <button type="button" @click="closeUserModal" class="btn-secondary flex-1">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Notification System -->
        <div class="fixed top-4 right-4 z-50 space-y-2">
            <div v-for="notification in notifications" :key="notification.id" 
                 :class="[
                     'px-6 py-4 rounded-lg shadow-lg text-white max-w-sm animate-slide-in',
                     notification.type === 'success' ? 'bg-green-500' : 
                     notification.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                 ]">
                <div class="flex items-center justify-between">
                    <span>{{ notification.message }}</span>
                    <button @click="removeNotification(notification.id)" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script>
        const { createApp } = Vue;
        
        const app = createApp({
            data() {
                return {
                    adminSidebarOpen: false,
                    userModalOpen: false,
                    editingUser: null,
                    searchQuery: '',
                    roleFilter: '',
                    statusFilter: '',
                    currentPage: 1,
                    itemsPerPage: 10,
                    notifications: [],
                    
                    userForm: {
                        name: '',
                        username: '',
                        email: '',
                        role: 'user',
                        status: 'active',
                        password: ''
                    },
                    
                    users: [
                        {
                            id: 1,
                            name: 'John Doe',
                            username: 'johndoe',
                            email: '<EMAIL>',
                            role: 'admin',
                            status: 'active',
                            lastLogin: '2024-01-15',
                            avatar: '../assets/images/user-1.jpg'
                        },
                        {
                            id: 2,
                            name: 'Jane Smith',
                            username: 'janesmith',
                            email: '<EMAIL>',
                            role: 'editor',
                            status: 'active',
                            lastLogin: '2024-01-14',
                            avatar: '../assets/images/user-2.jpg'
                        },
                        {
                            id: 3,
                            name: 'Mike Johnson',
                            username: 'mikej',
                            email: '<EMAIL>',
                            role: 'user',
                            status: 'inactive',
                            lastLogin: '2024-01-10',
                            avatar: '../assets/images/user-3.jpg'
                        },
                        {
                            id: 4,
                            name: 'Sarah Wilson',
                            username: 'sarahw',
                            email: '<EMAIL>',
                            role: 'editor',
                            status: 'active',
                            lastLogin: '2024-01-13',
                            avatar: '../assets/images/user-4.jpg'
                        }
                    ]
                }
            },
            
            computed: {
                filteredUsers() {
                    let filtered = this.users;
                    
                    if (this.searchQuery) {
                        filtered = filtered.filter(user => 
                            user.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                            user.email.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                            user.username.toLowerCase().includes(this.searchQuery.toLowerCase())
                        );
                    }
                    
                    if (this.roleFilter) {
                        filtered = filtered.filter(user => user.role === this.roleFilter);
                    }
                    
                    if (this.statusFilter) {
                        filtered = filtered.filter(user => user.status === this.statusFilter);
                    }
                    
                    return filtered;
                },
                
                totalPages() {
                    return Math.ceil(this.filteredUsers.length / this.itemsPerPage);
                },
                
                activeUsers() {
                    return this.users.filter(user => user.status === 'active').length;
                },
                
                adminUsers() {
                    return this.users.filter(user => user.role === 'admin').length;
                },
                
                newUsers() {
                    // Simulate new users this month
                    return 12;
                }
            },
            
            methods: {
                toggleAdminSidebar() {
                    this.adminSidebarOpen = !this.adminSidebarOpen;
                },
                
                openAddUserModal() {
                    this.editingUser = null;
                    this.userForm = {
                        name: '',
                        username: '',
                        email: '',
                        role: 'user',
                        status: 'active',
                        password: ''
                    };
                    this.userModalOpen = true;
                },
                
                editUser(user) {
                    this.editingUser = user;
                    this.userForm = { ...user };
                    this.userModalOpen = true;
                },
                
                closeUserModal() {
                    this.userModalOpen = false;
                    this.editingUser = null;
                },
                
                saveUser() {
                    if (this.editingUser) {
                        // Update existing user
                        const index = this.users.findIndex(u => u.id === this.editingUser.id);
                        this.users[index] = { ...this.userForm };
                        this.showNotification('User updated successfully', 'success');
                    } else {
                        // Add new user
                        const newUser = {
                            ...this.userForm,
                            id: Date.now(),
                            lastLogin: 'Never',
                            avatar: `https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=${this.userForm.name.charAt(0)}`
                        };
                        this.users.push(newUser);
                        this.showNotification('User added successfully', 'success');
                    }
                    
                    this.closeUserModal();
                },
                
                deleteUser(userId) {
                    if (confirm('Are you sure you want to delete this user?')) {
                        this.users = this.users.filter(user => user.id !== userId);
                        this.showNotification('User deleted successfully', 'success');
                    }
                },
                
                previousPage() {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                    }
                },
                
                nextPage() {
                    if (this.currentPage < this.totalPages) {
                        this.currentPage++;
                    }
                },
                
                showNotification(message, type = 'info') {
                    const notification = {
                        id: Date.now(),
                        message,
                        type
                    };
                    
                    this.notifications.push(notification);
                    
                    setTimeout(() => {
                        this.removeNotification(notification.id);
                    }, 5000);
                },
                
                removeNotification(id) {
                    this.notifications = this.notifications.filter(n => n.id !== id);
                }
            }
        });
        
        app.mount('#app');
    </script>
</body>
</html>
