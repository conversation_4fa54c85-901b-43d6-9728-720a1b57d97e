<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Multi-Purpose Template</title>
    <meta name="description" content="Admin login page for the multi-purpose template dashboard">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="../assets/css/tailwind.css" rel="stylesheet">
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans bg-gradient-to-br from-blue-50 to-purple-50 min-h-screen">
    <div id="app">
        <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
            <div class="max-w-md w-full space-y-8">
                <!-- Header -->
                <div class="text-center">
                    <a href="../html/index.html" class="text-3xl font-bold text-gradient mb-6 inline-block">
                        YourBrand
                    </a>
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">Admin Login</h2>
                    <p class="text-gray-600">Sign in to access the admin dashboard</p>
                </div>
                
                <!-- Login Form -->
                <div class="card-elevated">
                    <form @submit.prevent="submitLoginForm" class="space-y-6">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                Email
                            </label>
                            <input
                                type="email"
                                id="email"
                                v-model="loginForm.email"
                                class="input-field-google"
                                placeholder="Enter your email"
                                required
                            >
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                Password
                            </label>
                            <input
                                type="password"
                                id="password"
                                v-model="loginForm.password"
                                class="input-field-google"
                                placeholder="Enter your password"
                                required
                            >
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <input 
                                    id="remember-me" 
                                    name="remember-me" 
                                    type="checkbox" 
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                >
                                <label for="remember-me" class="ml-2 block text-sm text-gray-700">
                                    Remember me
                                </label>
                            </div>
                            
                            <div class="text-sm">
                                <a href="#" class="text-blue-600 hover:text-blue-500 transition-colors">
                                    Forgot your password?
                                </a>
                            </div>
                        </div>
                        
                        <button
                            type="submit"
                            :disabled="isLoading"
                            class="btn-primary w-full py-4 text-lg google-shadow hover:google-shadow-hover"
                            :class="{ 'opacity-50 cursor-not-allowed': isLoading }"
                        >
                            <span v-if="!isLoading">Sign in</span>
                            <span v-else class="flex items-center justify-center">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Signing in...
                            </span>
                        </button>
                    </form>
                    
                    <!-- Demo Credentials -->
                    <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <h3 class="text-sm font-medium text-blue-800 mb-2">Demo Credentials</h3>
                        <div class="text-sm text-blue-700 space-y-1">
                            <p><strong>Email:</strong> <EMAIL></p>
                            <p><strong>Password:</strong> admin123</p>
                        </div>
                        <button 
                            @click="fillDemoCredentials" 
                            class="mt-2 text-sm text-blue-600 hover:text-blue-500 underline"
                        >
                            Use Demo Credentials
                        </button>
                    </div>
                </div>
                
                <!-- Alternative Login Options -->
                <div class="card">
                    <div class="text-center">
                        <p class="text-sm text-gray-600 mb-4">Or sign in with</p>
                        <div class="grid grid-cols-2 gap-3">
                            <button class="btn-google">
                                <i class="fab fa-google text-red-500"></i>
                                <span>Google</span>
                            </button>
                            <button class="btn-google">
                                <i class="fab fa-microsoft text-blue-500"></i>
                                <span>Microsoft</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Footer Links -->
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        Don't have an account? 
                        <a href="register.html" class="text-blue-600 hover:text-blue-500 transition-colors font-medium">
                            Sign up here
                        </a>
                    </p>
                    <p class="text-sm text-gray-500 mt-2">
                        <a href="../html/index.html" class="hover:text-gray-700 transition-colors">
                            ← Back to website
                        </a>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Notification System -->
        <div class="fixed top-4 right-4 z-50 space-y-2">
            <div v-for="notification in notifications" :key="notification.id" 
                 :class="[
                     'px-6 py-4 rounded-lg shadow-lg text-white max-w-sm animate-slide-in',
                     notification.type === 'success' ? 'bg-green-500' : 
                     notification.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                 ]">
                <div class="flex items-center justify-between">
                    <span>{{ notification.message }}</span>
                    <button @click="removeNotification(notification.id)" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script>
        // Extend the main Vue app with login-specific methods
        const { createApp } = Vue;
        
        const app = createApp({
            data() {
                return {
                    loginForm: {
                        email: '',
                        password: ''
                    },
                    isLoading: false,
                    notifications: []
                }
            },
            
            methods: {
                validateEmail(email) {
                    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return re.test(email);
                },
                
                async submitLoginForm() {
                    if (!this.loginForm.email || !this.loginForm.password) {
                        this.showNotification('Please fill in all fields', 'error');
                        return;
                    }
                    
                    if (!this.validateEmail(this.loginForm.email)) {
                        this.showNotification('Please enter a valid email address', 'error');
                        return;
                    }
                    
                    this.isLoading = true;
                    
                    // Simulate API call
                    setTimeout(() => {
                        this.isLoading = false;
                        
                        // Check demo credentials
                        if (this.loginForm.email === '<EMAIL>' && this.loginForm.password === 'admin123') {
                            this.showNotification('Login successful! Redirecting...', 'success');
                            setTimeout(() => {
                                window.location.href = 'dashboard.html';
                            }, 1500);
                        } else {
                            this.showNotification('Invalid credentials. Please try again.', 'error');
                        }
                    }, 1500);
                },
                
                fillDemoCredentials() {
                    this.loginForm.email = '<EMAIL>';
                    this.loginForm.password = 'admin123';
                    this.showNotification('Demo credentials filled', 'info');
                },
                
                showNotification(message, type = 'info') {
                    const notification = {
                        id: Date.now(),
                        message,
                        type
                    };
                    
                    this.notifications.push(notification);
                    
                    setTimeout(() => {
                        this.removeNotification(notification.id);
                    }, 5000);
                },
                
                removeNotification(id) {
                    this.notifications = this.notifications.filter(n => n.id !== id);
                }
            }
        });
        
        app.mount('#app');
    </script>
</body>
</html>
