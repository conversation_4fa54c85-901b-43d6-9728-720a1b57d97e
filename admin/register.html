<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Register - Multi-Purpose Template</title>
    <meta name="description" content="Create a new admin account for the multi-purpose template dashboard">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans bg-gradient-to-br from-blue-50 to-purple-50 min-h-screen">
    <div id="app">
        <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
            <div class="max-w-md w-full space-y-8">
                <!-- Header -->
                <div class="text-center">
                    <a href="../html/index.html" class="text-3xl font-bold text-gradient mb-6 inline-block">
                        YourBrand
                    </a>
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">Create Admin Account</h2>
                    <p class="text-gray-600">Sign up to access the admin dashboard</p>
                </div>
                
                <!-- Register Form -->
                <div class="card">
                    <form @submit.prevent="submitRegisterForm" class="space-y-6">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">
                                    First Name
                                </label>
                                <input 
                                    type="text" 
                                    id="firstName" 
                                    v-model="registerForm.firstName"
                                    class="input-field"
                                    placeholder="John"
                                    required
                                >
                            </div>
                            <div>
                                <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">
                                    Last Name
                                </label>
                                <input 
                                    type="text" 
                                    id="lastName" 
                                    v-model="registerForm.lastName"
                                    class="input-field"
                                    placeholder="Doe"
                                    required
                                >
                            </div>
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                Email Address
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-envelope text-gray-400"></i>
                                </div>
                                <input 
                                    type="email" 
                                    id="email" 
                                    v-model="registerForm.email"
                                    class="input-field pl-10"
                                    placeholder="<EMAIL>"
                                    required
                                >
                            </div>
                        </div>
                        
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                Password
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400"></i>
                                </div>
                                <input 
                                    type="password" 
                                    id="password" 
                                    v-model="registerForm.password"
                                    class="input-field pl-10"
                                    placeholder="Enter your password"
                                    required
                                >
                            </div>
                        </div>
                        
                        <div>
                            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                Confirm Password
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400"></i>
                                </div>
                                <input 
                                    type="password" 
                                    id="confirmPassword" 
                                    v-model="registerForm.confirmPassword"
                                    class="input-field pl-10"
                                    placeholder="Confirm your password"
                                    required
                                >
                            </div>
                        </div>
                        
                        <div>
                            <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
                                Role
                            </label>
                            <select 
                                id="role" 
                                v-model="registerForm.role"
                                class="input-field"
                                required
                            >
                                <option value="">Select a role</option>
                                <option value="admin">Administrator</option>
                                <option value="editor">Editor</option>
                                <option value="user">User</option>
                            </select>
                        </div>
                        
                        <div class="flex items-center">
                            <input 
                                id="terms" 
                                name="terms" 
                                type="checkbox" 
                                v-model="registerForm.acceptTerms"
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                required
                            >
                            <label for="terms" class="ml-2 block text-sm text-gray-700">
                                I agree to the 
                                <a href="#" class="text-blue-600 hover:text-blue-500 transition-colors">Terms of Service</a>
                                and 
                                <a href="#" class="text-blue-600 hover:text-blue-500 transition-colors">Privacy Policy</a>
                            </label>
                        </div>
                        
                        <button 
                            type="submit" 
                            :disabled="isLoading"
                            class="btn-primary w-full py-3 text-lg"
                            :class="{ 'opacity-50 cursor-not-allowed': isLoading }"
                        >
                            <span v-if="!isLoading">Create Account</span>
                            <span v-else class="flex items-center justify-center">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Creating Account...
                            </span>
                        </button>
                    </form>
                    
                    <!-- Demo Note -->
                    <div class="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                        <h3 class="text-sm font-medium text-yellow-800 mb-2">Demo Mode</h3>
                        <p class="text-sm text-yellow-700">
                            This is a demo registration form. In a real application, this would create a new user account with proper backend validation and security.
                        </p>
                    </div>
                </div>
                
                <!-- Alternative Registration Options -->
                <div class="card">
                    <div class="text-center">
                        <p class="text-sm text-gray-600 mb-4">Or sign up with</p>
                        <div class="grid grid-cols-2 gap-3">
                            <button class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                                <i class="fab fa-google text-red-500 mr-2"></i>
                                Google
                            </button>
                            <button class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                                <i class="fab fa-microsoft text-blue-500 mr-2"></i>
                                Microsoft
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Footer Links -->
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        Already have an account? 
                        <a href="login.html" class="text-blue-600 hover:text-blue-500 transition-colors font-medium">
                            Sign in here
                        </a>
                    </p>
                    <p class="text-sm text-gray-500 mt-2">
                        <a href="../html/index.html" class="hover:text-gray-700 transition-colors">
                            ← Back to website
                        </a>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Notification System -->
        <div class="fixed top-4 right-4 z-50 space-y-2">
            <div v-for="notification in notifications" :key="notification.id" 
                 :class="[
                     'px-6 py-4 rounded-lg shadow-lg text-white max-w-sm animate-slide-in',
                     notification.type === 'success' ? 'bg-green-500' : 
                     notification.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                 ]">
                <div class="flex items-center justify-between">
                    <span>{{ notification.message }}</span>
                    <button @click="removeNotification(notification.id)" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script>
        // Register-specific Vue app
        const { createApp } = Vue;
        
        const app = createApp({
            data() {
                return {
                    registerForm: {
                        firstName: '',
                        lastName: '',
                        email: '',
                        password: '',
                        confirmPassword: '',
                        role: '',
                        acceptTerms: false
                    },
                    isLoading: false,
                    notifications: []
                }
            },
            
            methods: {
                validateEmail(email) {
                    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return re.test(email);
                },
                
                async submitRegisterForm() {
                    // Validation
                    if (!this.registerForm.firstName || !this.registerForm.lastName || 
                        !this.registerForm.email || !this.registerForm.password || 
                        !this.registerForm.confirmPassword || !this.registerForm.role) {
                        this.showNotification('Please fill in all fields', 'error');
                        return;
                    }
                    
                    if (!this.validateEmail(this.registerForm.email)) {
                        this.showNotification('Please enter a valid email address', 'error');
                        return;
                    }
                    
                    if (this.registerForm.password.length < 6) {
                        this.showNotification('Password must be at least 6 characters long', 'error');
                        return;
                    }
                    
                    if (this.registerForm.password !== this.registerForm.confirmPassword) {
                        this.showNotification('Passwords do not match', 'error');
                        return;
                    }
                    
                    if (!this.registerForm.acceptTerms) {
                        this.showNotification('Please accept the terms and conditions', 'error');
                        return;
                    }
                    
                    this.isLoading = true;
                    
                    // Simulate API call
                    setTimeout(() => {
                        this.isLoading = false;
                        this.showNotification('Account created successfully! Redirecting to login...', 'success');
                        
                        setTimeout(() => {
                            window.location.href = 'login.html';
                        }, 2000);
                    }, 1500);
                },
                
                showNotification(message, type = 'info') {
                    const notification = {
                        id: Date.now(),
                        message,
                        type
                    };
                    
                    this.notifications.push(notification);
                    
                    setTimeout(() => {
                        this.removeNotification(notification.id);
                    }, 5000);
                },
                
                removeNotification(id) {
                    this.notifications = this.notifications.filter(n => n.id !== id);
                }
            }
        });
        
        app.mount('#app');
    </script>
</body>
</html>
